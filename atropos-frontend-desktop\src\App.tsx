import React, { useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import PinLoginPage from './pages/PinLoginPage';
import DashboardPage from './pages/DashboardPage'; // DashboardPage bileşenini içeri aktarın
import ProtectedRoute from './components/ProtectedRoute';
import { useAuth } from './context/AuthContext';
import { Box } from '@chakra-ui/react';

function App() {
  const { isAuthenticated, login, logout, accessToken } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    } else {
      if (window.location.pathname !== '/login/pin') {
         navigate('/login/pin');
      }
    }
  }, [isAuthenticated, navigate]);

  return (
    <Box minH="100vh" bg="gray.50">
      <Routes>
        {/* PIN Login Route */}
        <Route path="/login/pin" element={<PinLoginPage onLoginSuccess={login} />} />

        {/* <PERSON><PERSON><PERSON> rotalar */}
        <Route
          path="/"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              {/* HomePage yerine DashboardPage'i render ediyoruz */}
              <DashboardPage />
            </ProtectedRoute>
          }
        />

        {/* Tanımlanmayan tüm diğer rotalar için yönlendirme */}
        <Route path="*" element={<PinLoginPage onLoginSuccess={login} />} /> {/* Giriş yapılmamışsa PIN login'e */}
      </Routes>
    </Box>
  );
}

export default App;