import React, { useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage'; // DashboardPage bileşenini içeri aktarın
import ProtectedRoute from './components/ProtectedRoute';
import { useAuth } from './context/AuthContext';
import { Box } from '@chakra-ui/react';

function App() {
  const { isAuthenticated, login, logout, accessToken } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    } else {
      if (window.location.pathname !== '/login') {
         navigate('/login');
      }
    }
  }, [isAuthenticated, navigate]);

  return (
    <Box minH="100vh" bg="gray.50">
      <Routes>
        {/* Her zaman erişilebilir rotalar */}
        <Route path="/login" element={<LoginPage onLoginSuccess={login} />} /> {/* useAuth'tan gelen login fonksi<PERSON><PERSON>u kullan */}

        {/* <PERSON><PERSON>an rotalar */}
        <Route
          path="/"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              {/* HomePage yerine DashboardPage'i render ediyoruz */}
              <DashboardPage />
            </ProtectedRoute>
          }
        />

        {/* Tanımlanmayan tüm diğer rotalar için yönlendirme */}
        <Route path="*" element={<LoginPage onLoginSuccess={login} />} /> {/* Giriş yapılmamışsa login'e */}
      </Routes>
    </Box>
  );
}

export default App;