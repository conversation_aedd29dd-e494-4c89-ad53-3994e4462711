'use strict';

var jsxRuntime = require('react/jsx-runtime');
var react = require('@chakra-ui/react');

const QuestionOutlineIcon = react.createIcon({
  displayName: "QuestionOutlineIcon",
  path: /* @__PURE__ */ jsxRuntime.jsxs("g", { stroke: "currentColor", strokeWidth: "1.5", children: [
    /* @__PURE__ */ jsxRuntime.jsx(
      "path",
      {
        strokeLinecap: "round",
        fill: "none",
        d: "M9,9a3,3,0,1,1,4,2.829,1.5,1.5,0,0,0-1,1.415V14.25"
      }
    ),
    /* @__PURE__ */ jsxRuntime.jsx(
      "path",
      {
        fill: "none",
        strokeLinecap: "round",
        d: "M12,17.25a.375.375,0,1,0,.375.375A.375.375,0,0,0,12,17.25h0"
      }
    ),
    /* @__PURE__ */ jsxRuntime.jsx("circle", { fill: "none", strokeMiterlimit: "10", cx: "12", cy: "12", r: "11.25" })
  ] })
});

exports.QuestionOutlineIcon = QuestionOutlineIcon;
