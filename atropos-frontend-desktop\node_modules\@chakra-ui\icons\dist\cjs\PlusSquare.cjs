'use strict';

var jsxRuntime = require('react/jsx-runtime');
var react = require('@chakra-ui/react');

const PlusSquareIcon = react.createIcon({
  displayName: "PlusSquareIcon",
  path: /* @__PURE__ */ jsxRuntime.jsxs("g", { fill: "none", stroke: "currentColor", strokeLinecap: "round", strokeWidth: "2", children: [
    /* @__PURE__ */ jsxRuntime.jsx("rect", { height: "18", width: "18", rx: "2", ry: "2", x: "3", y: "3" }),
    /* @__PURE__ */ jsxRuntime.jsx("path", { d: "M12 8v8" }),
    /* @__PURE__ */ jsxRuntime.jsx("path", { d: "M8 12h8" })
  ] })
});

exports.PlusSquareIcon = PlusSquareIcon;
