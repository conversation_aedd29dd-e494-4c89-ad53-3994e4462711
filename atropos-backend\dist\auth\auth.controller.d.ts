import { AuthService } from './auth.service';
import { LoginPinDto } from './dto/login-pin.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    login(req: any): Promise<{
        access_token: string;
    }>;
    loginWithPin(loginPinDto: LoginPinDto): Promise<{
        access_token: string;
    }>;
    getProfile(req: any): any;
    getAdminDashboard(req: any): string;
}
