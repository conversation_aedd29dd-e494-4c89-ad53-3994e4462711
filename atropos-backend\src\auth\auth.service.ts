// src/auth/auth.service.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UserService } from '../user/user.service'; // UserService'i import et
import { JwtService } from '@nestjs/jwt'; // JwtService'i import et
import * as bcrypt from 'bcryptjs'; // bcrypt'i import et
import { User } from '../../generated/prisma'; // Prisma'dan User modelini import et

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService, // Kullanıcıları doğrulamak için
    private jwtService: JwtService, // JWT token'ları oluşturmak için
  ) {}

  async validateUser(username: string, pass: string): Promise<any | null> {
    const user = await this.userService.findByUsername(username); // findByUsername metodunu UserServis'e ekleyeceğiz
    if (user && user.active && !user.deletedAt && !user.lockedUntil) { // Kullanıcı aktif ve silinmemiş/kilitli de<PERSON>
      const isPasswordValid = await bcrypt.compare(pass, user.password);
      if (isPasswordValid) {
        // Şifre hash'ini ve refresh token'ı döndürme
        const { password, refreshToken, ...result } = user; // Hassas bilgileri çıkar
        return result;
      }
    }
    return null;
  }

  async validateUserByPin(username: string, pin: string): Promise<any | null> {
    const user = await this.userService.findByUsername(username);
    if (user && user.active && !user.deletedAt && !user.lockedUntil && user.pin) {
      const isPinValid = await bcrypt.compare(pin, user.pin);
      if (isPinValid) {
        // Şifre hash'ini ve refresh token'ı döndürme
        const { password, refreshToken, pin: userPin, ...result } = user;
        return result;
      }
    }
    return null;
  }

  async login(user: any) { // user, validateUser'dan gelen User objesi olacak
    const payload = { username: user.username, sub: user.id, role: user.role };
    // lastLoginAt güncellemesi ve failedLoginCount sıfırlaması
    await this.userService.updateUser(user.id, { lastLoginAt: new Date(), failedLoginCount: 0 }); // updateUser metodunu kullanacağız

    return {
      access_token: this.jwtService.sign(payload),
      // Refresh Token (ileride eklenebilir)
    };
  }
}
