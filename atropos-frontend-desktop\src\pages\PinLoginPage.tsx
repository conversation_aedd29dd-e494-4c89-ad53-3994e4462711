import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import {
  Box,
  Button,
  Flex,
  VStack,
  HStack,
  Text,
  Heading,
  Alert,
  AlertIcon,
  Image,
  Container,
  Grid,
  GridItem,
  IconButton,
} from '@chakra-ui/react';
import { DeleteIcon } from '@chakra-ui/icons';

interface PinLoginPageProps {
  onLoginSuccess: (token: string) => void;
}

const PinLoginPage: React.FC<PinLoginPageProps> = ({ onLoginSuccess }) => {
  const { t } = useTranslation('login');
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const [enteredPin, setEnteredPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Fixed username for now as specified in task
  const username = 'kasiyer1';
  
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  const handleNumberClick = (number: string) => {
    if (enteredPin.length < 4) {
      const newPin = enteredPin + number;
      setEnteredPin(newPin);
      
      // Auto-submit when 4 digits are entered
      if (newPin.length === 4) {
        handlePinLogin(newPin);
      }
    }
  };

  const handleDelete = () => {
    setEnteredPin(enteredPin.slice(0, -1));
  };

  const handlePinLogin = async (pin: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/auth/login/pin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, pin }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t('pin_login_failed'));
      }

      const data = await response.json();
      const token = data.access_token;
      localStorage.setItem('accessToken', token);
      login(token);
      navigate('/');

    } catch (err: any) {
      setError(err.message);
      setEnteredPin(''); // Clear PIN on error
      console.error('PIN login error:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderPinDots = () => {
    return (
      <HStack spacing={3}>
        {[0, 1, 2, 3].map((index) => (
          <Box
            key={index}
            w="52px"
            h="50px"
            bg="gray.100"
            borderRadius="xl"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            {enteredPin[index] && (
              <Box
                w="12px"
                h="12px"
                bg="gray.800"
                borderRadius="full"
              />
            )}
          </Box>
        ))}
      </HStack>
    );
  };

  const numberPadButtons = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['.', '0', 'delete']
  ];

  return (
    <Flex minH="100vh" bg="white">
      {/* Left Side - Background Image with Quote */}
      <Box
        flex="1"
        bgGradient="linear(to-b, transparent 28%, blackAlpha.600 100%)"
        bgImage="url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3')"
        bgSize="cover"
        bgPosition="center"
        position="relative"
        display={{ base: "none", lg: "flex" }}
        flexDirection="column"
        justifyContent="flex-end"
        p={10}
      >
        {/* Quote Section */}
        <VStack align="start" spacing={6} color="white" maxW="560px">
          <Text fontSize="20px" lineHeight="1.2">
            "
          </Text>
          <Text fontSize="24px" fontWeight="medium" lineHeight="1.5">
            {t('quote_text', 'Every contact we have with a customer influences whether or not they\'ll come back. We have to be great every time or we\'ll lose them.')}
          </Text>
          <Box
            border="1px solid white"
            borderRadius="full"
            px={4}
            py={2}
          >
            <Text fontSize="14px" fontWeight="medium">
              {t('quote_author', 'Kevin Stirtz, Author')}
            </Text>
          </Box>
        </VStack>

        {/* Progress Slider */}
        <HStack spacing={2} position="absolute" bottom="40px" left="40px">
          <Box w="10px" h="3px" bg="white" borderRadius="3px" />
          <Box w="10px" h="3px" bg="gray.400" borderRadius="3px" />
          <Box w="10px" h="3px" bg="gray.400" borderRadius="3px" />
        </HStack>
      </Box>

      {/* Right Side - Login Form */}
      <Box
        w={{ base: "100%", lg: "524px" }}
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="space-between"
        p={8}
        bg="white"
      >
        {/* Logo */}
        <Box h="56px" w="89px" mb={8}>
          <Text fontSize="24px" fontWeight="bold" color="blue.600">
            Atropos POS
          </Text>
        </Box>

        {/* Title */}
        <Heading
          size="lg"
          color="blue.600"
          textAlign="center"
          fontWeight="bold"
          fontSize="28px"
          mb={8}
        >
          {t('employee_login', 'Employee Login')}
        </Heading>

        {/* PIN Input Section */}
        <VStack spacing={6} w="full" maxW="382px">
          {/* PIN Form */}
          <VStack spacing={3}>
            <Text fontSize="14px" color="gray.600" textAlign="center">
              {t('enter_pin_instruction', 'Enter your PIN to validate yourself.')}
            </Text>
            {renderPinDots()}
          </VStack>

          {/* Error Message */}
          {error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              {error}
            </Alert>
          )}

          {/* Number Pad */}
          <Grid templateColumns="repeat(3, 1fr)" gap={4} w="full" maxW="240px">
            {numberPadButtons.flat().map((button, index) => (
              <GridItem key={index}>
                {button === 'delete' ? (
                  <IconButton
                    aria-label="Delete"
                    icon={<DeleteIcon />}
                    onClick={handleDelete}
                    isDisabled={loading || enteredPin.length === 0}
                    variant="ghost"
                    size="lg"
                    h="54px"
                    w="64px"
                    fontSize="24px"
                  />
                ) : button === '.' ? (
                  <Button
                    onClick={() => {}} // Disabled for PIN input
                    isDisabled={true}
                    variant="ghost"
                    size="lg"
                    h="54px"
                    w="64px"
                    fontSize="28px"
                    fontWeight="normal"
                  >
                    .
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleNumberClick(button)}
                    isDisabled={loading}
                    variant="ghost"
                    size="lg"
                    h="54px"
                    w="64px"
                    fontSize="28px"
                    fontWeight="medium"
                    color="gray.800"
                    _hover={{ bg: "gray.100" }}
                  >
                    {button}
                  </Button>
                )}
              </GridItem>
            ))}
          </Grid>
        </VStack>

        {/* Start Shift Button */}
        <Button
          colorScheme="blue"
          size="lg"
          w="full"
          maxW="382px"
          h="60px"
          borderRadius="xl"
          fontSize="16px"
          fontWeight="semibold"
          isLoading={loading}
          loadingText={t('validating', 'Validating...')}
          isDisabled={enteredPin.length !== 4}
          onClick={() => handlePinLogin(enteredPin)}
        >
          {t('start_shift', 'Start Shift')}
        </Button>
      </Box>
    </Flex>
  );
};

export default PinLoginPage;
