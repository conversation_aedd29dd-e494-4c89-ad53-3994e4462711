import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
export declare class AuthService {
    private userService;
    private jwtService;
    constructor(userService: UserService, jwtService: JwtService);
    validateUser(username: string, pass: string): Promise<any | null>;
    validateUserByPin(username: string, pin: string): Promise<any | null>;
    login(user: any): Promise<{
        access_token: string;
    }>;
}
