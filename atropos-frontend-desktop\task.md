<PERSON><PERSON><PERSON><PERSON><PERSON>, endişenizi gidermek için bura<PERSON>ı<PERSON>. Projenizin dosya yapısını, yani hem `atropos-backend` hem de `atropos-frontend-desktop` kısımlarını çok dikkatli bir şekilde inceledim. <PERSON><PERSON><PERSON> talim<PERSON>lar da tam olarak bu mevcut yapıya uygun şekilde hazırlanmıştır.

Şimdi size çoklu dil desteği altyapısını kurmanız için o talimatları, dosya konumlarını da özellikle belirterek tekrar sunuyorum:

**Çoklu Dil Desteği (i18n) Kurulumu - Adım Adım Talimatlar**

React (Vite) ön yüz projenizde (`atropos-frontend-desktop` klasörü içinde) bu altyapıyı `react-i18next` kütüphanesini kullanarak oluşturacağız.

-----

### Adım 1: Gerekli Paketleri Ku<PERSON>

Ö<PERSON>likle, **`atropos-frontend-desktop`** klasörünüzün kök dizininde terminali açın ve şu paketleri kurun:

```bash
npm install i18next react-i18next i18next-browser-languagedetector i18next-http-backend
# veya
yarn add i18next react-i18next i18next-browser-languagedetector i18next-http-backend
```

  * `i18next`: Uluslararasılaşma mantığının çekirdek kütüphanesi.
  * `react-i18next`: `i18next`'i React ile entegre etmek için gerekli React bileşenleri ve hook'ları sağlar.
  * `i18next-browser-languagedetector`: Kullanıcının tarayıcı dilini veya daha önceki bir ayarını (localStorage'dan) otomatik olarak algılamak için kullanılır.
  * `i18next-http-backend`: Çeviri dosyalarını HTTP üzerinden yüklemek için kullanılır. Bu, JSON dosyalarınızı `public` klasöründe tutmamızı sağlar.

-----

### Adım 2: Çeviri Dosyalarınızı Oluşturun

Çevirileri tutacağınız JSON dosyalarını projenizin **`atropos-frontend-desktop/public`** klasörü altında aşağıdaki gibi bir dizin yapısıyla oluşturun:

```
atropos-frontend-desktop/
└── public/
    └── locales/
        ├── en/
        │   └── translation.json
        └── tr/
            └── translation.json
```

**`atropos-frontend-desktop/public/locales/en/translation.json` İçeriği (İngilizce):**

```json
{
  "welcome": "Welcome to Atropos POS",
  "change_language": "Change Language",
  "login": "Login",
  "username": "Username",
  "password": "Password",
  "company_name": "Company Name:",
  "loading": "Loading...",
  "error_fetching_data": "Error fetching data."
}
```

**`atropos-frontend-desktop/public/locales/tr/translation.json` İçeriği (Türkçe):**

```json
{
  "welcome": "Atropos POS'a Hoş Geldiniz",
  "change_language": "Dil Değiştir",
  "login": "Giriş Yap",
  "username": "Kullanıcı Adı",
  "password": "Şifre",
  "company_name": "Şirket Adı:",
  "loading": "Yükleniyor...",
  "error_fetching_data": "Veri alınırken hata oluştu."
}
```

Bu yapı, gelecekte başka diller eklemek istediğinizde (örn: `fr` için `public/locales/fr/translation.json`) çok düzenli olacaktır.

-----

### Adım 3: i18n Konfigürasyonunu Oluşturun

**`atropos-frontend-desktop/src`** klasörünüzün içine yeni bir `i18n.ts` dosyası oluşturun. Bu dosya i18n kütüphanesini başlatacak ve temel ayarları yapacaktır.

**`atropos-frontend-desktop/src/i18n.ts` İçeriği:**

```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend'; // Backend'i içeri aktar

i18n
  .use(Backend) // Çeviri dosyalarını yüklemek için backend'i kullan
  .use(LanguageDetector) // Dil algılayıcıyı kullan
  .use(initReactI18next) // i18n'i React'e bağla
  .init({
    fallbackLng: "tr", // Eğer bir çeviri bulunamazsa kullanılacak varsayılan dil
    debug: false, // Geliştirme aşamasında true yapabilirsiniz, üretimde false olmalı
    interpolation: {
      escapeValue: false // React XSS'ye karşı zaten korur
    },
    detection: {
      order: ['localStorage', 'navigator'], // Dili algılama sırası (önce kaydedilen dil, sonra tarayıcı dili)
      caches: ['localStorage'], // Algılanan dili nerede saklayacağı
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json', // Çeviri dosyalarınızın yolu
      // {{lng}} mevcut dili, {{ns}} namespace'i (varsayılan 'translation') temsil eder
    }
  });

export default i18n;
```

-----

### Adım 4: `main.tsx` Dosyasını Güncelleyin

Uygulamanızın başlangıç noktası olan **`atropos-frontend-desktop/src/main.tsx`** dosyasını, `i18n` konfigürasyonunu içeri aktararak ve `I18nextProvider` bileşenini kullanarak güncelleyin. Bu, tüm uygulamanızın i18n bağlamına erişmesini sağlayacak.

**`atropos-frontend-desktop/src/main.tsx` İçeriği:**

```typescript
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import './i18n'; // i18n konfigürasyonunu içeri aktarın
import { I18nextProvider } from 'react-i18next'; // I18nextProvider'ı içeri aktarın
import i18n from './i18n'; // i18n instance'ınızı içeri aktarın

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    {/* Uygulamanızın tamamını I18nextProvider ile sarın */}
    <I18nextProvider i18n={i18n}>
      <App />
    </I18nextProvider>
  </StrictMode>,
);
```

-----

### Adım 5: Bileşenlerinizde Çevirileri Kullanın

Artık React bileşenlerinizde `useTranslation` hook'unu kullanarak çeviri anahtarlarını kolayca kullanabilirsiniz.

**Örnek: `atropos-frontend-desktop/src/App.tsx` Dosyasını Güncelleyin**

```typescript
import { useState, useEffect } from 'react';
import './App.css'; // Mevcut CSS'i kullanabiliriz
import { useTranslation } from 'react-i18next'; // useTranslation hook'unu içeri aktarın

interface Company {
  id: string;
  name: string;
  taxNumber: string;
  taxOffice: string;
  address: string;
  phone: string;
  email?: string;
  // ... diğer Company alanları
}

function App() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // useTranslation hook'unu kullanarak t (translate) fonksiyonuna erişin
  const { t, i18n } = useTranslation();

  // Backend API URL'ini ortam değişkeninden al
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'; // Fallback olarak localhost:3000

  useEffect(() => {
    // Bileşen yüklendiğinde şirketleri otomatik olarak çek
    fetchCompanies();
  }, []); // Sadece bir kere yüklensin

  const fetchCompanies = async () => {
    setLoading(true);
    setError(null);
    try {
      // Backend API'mize GET isteği gönder
      const response = await fetch(`${API_URL}/company`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `${t('error_fetching_data')} status: ${response.status}`);
      }
      const data: Company[] = await response.json();
      setCompanies(data);
    } catch (err: any) {
      setError(err.message);
      console.error(t('error_fetching_data'), err);
    } finally {
      setLoading(false);
    }
  };

  // Dil değiştirme fonksiyonu
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="App">
      <h1>{t('welcome')}</h1> {/* Çevrilmiş metin */}
      <p>{t('change_language')}:</p>
      <div>
        <button onClick={() => changeLanguage('tr')}>Türkçe</button>
        <button onClick={() => changeLanguage('en')}>English</button>
      </div>

      <button onClick={fetchCompanies} disabled={loading}>
        {loading ? t('loading') : t('reload_companies')} {/* Çevrilmiş metin */}
      </button>

      {error && <p style={{ color: 'red' }}>Hata: {error}</p>}

      <h2>{t('available_companies')}:</h2> {/* Yeni çeviri anahtarı */}
      {companies.length === 0 && !loading && !error && <p>{t('no_companies_found')}.</p>} {/* Yeni çeviri anahtarı */}
      {companies.length > 0 && (
        <ul>
          {companies.map((company) => (
            <li key={company.id}>
              <strong>{company.name}</strong> ({t('tax_number')}: {company.taxNumber}) - {company.address} {/* Yeni çeviri anahtarı */}
            </li>
          ))}
        </ul>
      )}
      <p>API URL: {API_URL}</p>
    </div>
  );
}

export default App;
```

**Not:** `App.tsx` dosyasındaki `t('reload_companies')`, `t('available_companies')`, `t('no_companies_found')`, `t('tax_number')` gibi yeni çeviri anahtarlarını `public/locales/en/translation.json` ve `public/locales/tr/translation.json` dosyalarınıza eklemeyi unutmayın.

-----

### Backend (NestJS) İçin Notlar

Arka uçta (NestJS) çoklu dil desteği genellikle kullanıcı arayüzüne yönelik metinler (hata mesajları, doğrulama mesajları, API yanıtlarındaki açıklamalar) veya veritabanında saklanan çok dilli içerikler (ürün adları/açıklamaları gibi) için düşünülür.

Şu anda önceliğimiz ön yüzdeki metinleri uluslararasılaştırmak olduğundan, arka uç için özel bir entegrasyona şimdilik gerek yok. Ancak ileride API yanıtlarınızın da çok dilli olması gerekirse, NestJS için `nestjs-i18n` gibi kütüphaneleri inceleyebilir veya API yanıtlarınızda çeviri anahtarları döndürüp çeviriyi ön yüzün yapmasını sağlayabilirsiniz.

Bu adımları tamamladığınızda, projenizde çoklu dil desteği altyapısı hazır olacak ve yeni diller eklemek çok daha kolay hale gelecektir. Başarılar\!