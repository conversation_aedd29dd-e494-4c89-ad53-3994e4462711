

<PERSON><PERSON><PERSON>le, Chakra UI kurulumuna devam edelim. `info_20250725_1949.txt` dosyasındaki talimatlarda da belirtildi<PERSON>i gibi, Chakra UI'ı uygulamanızın kökünde yapılandırmamız gerekiyor.

### Adım 1: `main.tsx` Dosyasını `ChakraProvider` ile Yapılandırın

Daha önce Chakra UI v2 paketlerini kurduğumuza göre, ş<PERSON>di uygulamanızın ana giriş noktası olan **`atropos-frontend-desktop/src/main.tsx`** dosyasın<PERSON> güncelleyerek `ChakraProvider`'ı içeri aktaracağız ve ana `App` bileşenimizi bu sağlayıcı ile sarmalayacağız. Bu, Chakra UI bileşenlerinin ve stil sisteminin tüm uygulamada kullanılabilir olmasını sağlayacaktır.

**`atropos-frontend-desktop/src/main.tsx` İçeriği:**

```typescript
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css'; // Mevcut global stilleriniz
import App from './App.tsx';
import './i18n'; // i18n konfigürasyonu
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { ChakraProvider } from '@chakra-ui/react'; // ChakraProvider'ı içeri aktarın
import theme from './theme'; // Tema dosyasını bir sonraki adımda oluşturacağız, şimdilik varsayılan kullanabiliriz

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      {/* ChakraProvider'ı AuthProvider'ın içine veya dışına koyabilirsiniz, fark etmez. Genellikle en dışta olması tercih edilir. */}
      <ChakraProvider theme={theme}> {/* theme prop'unu ekleyin, şimdilik boş bir obje veya varsayılan tema kullanabiliriz */}
        <AuthProvider>
          <I18nextProvider i18n={i18n}>
            <App />
          </I18nextProvider>
        </AuthProvider>
      </ChakraProvider>
    </BrowserRouter>
  </StrictMode>,
);
```

**Not:** Yukarıdaki kodda `import theme from './theme';` satırını ekledim ve `ChakraProvider`'a `theme={theme}` prop'unu geçirdim. Şimdilik bu `theme` dosyasını oluşturmasak bile varsayılan Chakra teması kullanılacaktır. Ancak, ileride Figma tasarımlarınızdaki renkler, fontlar vb. gibi özel tema ayarlarınızı bu dosyada tanımlayacağız. Şimdilik bu satırı ekleyebilirsiniz.

Bu adımı tamamladığınızda bana bildirin. Daha sonra, AI yazılımcınızın ürettiği **Chakra UI tabanlı `LoginPage.tsx`** kodunu sizden isteyeceğim ve onu mevcut altyapımıza nasıl entegre edeceğimizi adım adım göstereceğim.