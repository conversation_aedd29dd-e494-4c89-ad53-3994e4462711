Tamamdır! <PERSON><PERSON> al<PERSON>. <PERSON><PERSON>, proje<PERSON><PERSON> doğ<PERSON><PERSON> geliştirecek olan AI yazılımcıya net ve kapsamlı bir komut vermek. İsteğiniz üzerine, tüm detayları ve beklentileri içeren, sanki doğrudan bir AI'a konuşuyormuş gibi bir "prompt" hazırlıyorum.

---

**🎉 AI Geliştiriciye Not: PIN ile Giriş Sistemi Full Stack Geliştirme Planı 🎉**

**Hedef:** Atropos POS uygulamasına Figma tasarımlarına tamamen uygun, tam yığın (Full Stack) bir PIN ile giriş sistemi entegre et.

---

**📋 Figma Bilgisi (Tasarım Referansları):**

<PERSON>u linkler, PIN ile giriş sayfasının farklı durumlarını (varsayılan, PIN girilmiş, hata durumu vb.) temsil etmektedir. Tasarımda bu görsel referanslara birebir sadık kalınacaktır. Özellikle karanlık temaya ve responsive tasarıma dikkat et.

* **Figma URL'leri (Karanlık Tema):**
    https://www.figma.com/design/1E7bcmE1NXSXvn8RRml6uC/Bitepoint---Restaurant-POS-App?node-id=714-72853&m=dev 

	https://www.figma.com/design/1E7bcmE1NXSXvn8RRml6uC/Bitepoint---Restaurant-POS-App?node-id=714-72918&m=dev 
    
	https://www.figma.com/design/1E7bcmE1NXSXvn8RRml6uC/Bitepoint---Restaurant-POS-App?node-id=714-72983&m=dev 

---

**🔧 Ek Talimatlar (Kritik Gereksinimler):**

* **Electron Uyumu:** Uygulama Electron ile tam ekran (full-screen layout) çalışacağı için viewport sorunları olmadan geniş ekranlara optimize edilecek.
* **Sayfa Düzeni ve Duyarlılık:** Figma tasarımına birebir sadık kalınacak ancak tüm cihazlarda (masaüstü, tablet, mobil) esnek ve duyarlı (responsive) olacak şekilde Chakra UI'ın responsive prop'ları (`base`, `md`, `lg` vb.) kullanılacak.
* **Görsel Elementlerin Yönetimi:** Yetkisi olmayan veya kullanılamaz olan menü öğeleri, butonlar ve diğer UI elementleri **kesinlikle gizlenmeyecek**, bunun yerine **devre dışı (disabled)** bırakılacak ve bu durum görsel olarak belirtilecek (soluk renk, tıklanamaz imleç vb.).

---

### **Bölüm 1: Backend Geliştirmeleri (NestJS - `atropos-backend` Projesi)**

1.  **`User` Modeline PIN Alanı Ekleme:**
    * `prisma/schema.prisma` dosyasındaki `User` modeline `pinHash String?` alanını ekle.
    * Prisma migration'ı oluştur ve çalıştır (`npx prisma migrate dev --name add_pin_to_user`).

2.  **PIN Hashleme Fonksiyonelliği:**
    * `src/user/user.service.ts` içinde veya uygun bir yardımcı modülde, PIN'leri güvenli bir şekilde hash'lemek için (örneğin `bcrypt` kullanarak) bir fonksiyon geliştir. Mevcut password hashleme mekanizmasına benzer olacak.

3.  **PIN ile Giriş API Endpoint'i Oluşturma:**
    * `src/auth/dto/login-pin.dto.ts` adında yeni bir DTO oluştur. Bu DTO `username` (veya `userId`) ve `pin` alanlarını içerecek.
    * `src/auth/auth.controller.ts` içinde yeni bir `POST /auth/login/pin` endpoint'i tanımla. Bu endpoint `LoginPinDto`'yu alacak.
    * `src/auth/auth.service.ts` içinde `validateUserByPin(username, pin)` adında yeni bir fonksiyon oluştur. Bu fonksiyon:
        * Veritabanından kullanıcıyı kullanıcı adına göre bulacak.
        * Sağlanan PIN'i kullanıcının `pinHash`'i ile karşılaştıracak.
        * Eğer PIN doğruysa, kullanıcının rol bilgilerini içeren bir JWT (`access_token` adıyla) döndürecek.
        * PIN yanlışsa veya kullanıcı bulunamazsa `UnauthorizedException` fırlatacak.

---

### **Bölüm 2: Frontend Geliştirmeleri (React / Chakra UI v2 - `atropos-frontend-desktop` Projesi)**

1.  **Chakra UI Entegrasyon Onayı:**
    * `atropos-frontend-desktop/src/main.tsx` dosyasında `ChakraProvider`'ın uygulamanın kökünü sardığından emin ol (`<ChakraProvider theme={theme}>`).
    * `atropos-frontend-desktop/src/theme.ts` dosyasının mevcut olduğundan emin ol (karanlık tema için varsayılan Chakra renklerini veya Figma'daki özel renkleri içerecek şekilde ayarlanacak).

2.  **PIN Giriş Sayfası Oluşturma (`PinLoginPage.tsx`):**
    * `atropos-frontend-desktop/src/pages/PinLoginPage.tsx` adında yeni bir bileşen dosyası oluştur.
    * Figma tasarımlarındaki PIN giriş arayüzünü (sayısal tuş takımı, PIN gösterim inputları, görsel alan vb.) bu bileşenin içine Chakra UI bileşenlerini (`Box`, `Flex`, `VStack`, `HStack`, `Input`, `Button`, `Text`, `Image`, `Alert` vb.) kullanarak inşa et.
    * **Önemli:** Sadece Chakra UI bileşenlerini kullan, ham HTML elementleri ve manuel CSS modülleri yerine Chakra'nın prop tabanlı stil sistemini tercih et.

3.  **Frontend Logic Entegrasyonu:**
    * **Durum Yönetimi:** `useState` hook'larını kullanarak PIN girişini (`enteredPin`), yüklenme durumunu (`loading`) ve hata mesajlarını (`error`) yönet.
    * **API Çağrısı:** PIN tuş takımından gelen girişleri işleyerek (örn: 4 haneye ulaştığında otomatik olarak) backend'deki `POST /auth/login/pin` endpoint'ine API çağrısı yapacak bir `async` fonksiyon (örn: `handlePinLogin`) oluştur. Kullanıcı adı (`username`) sabit bir değer olabilir (örn: 'kasiyer1') veya tasarımda bir kullanıcı seçimi varsa ona göre ayarlanır.
    * **Kimlik Doğrulama:** Backend'den başarılı yanıt geldiğinde, `useAuth()` hook'undan gelen `login(data.access_token)` fonksiyonunu çağırarak kullanıcının oturumunu başlat.
    * **Yönlendirme:** `useNavigate()` hook'unu kullanarak giriş başarılı olduktan sonra kullanıcıyı ana sayfaya (`/`) yönlendir.
    * **Hata ve Yüklenme Bildirimi:** `error` durumu için Chakra UI'ın `Alert` bileşenini kullan. `loading` durumu için giriş butonlarını `isLoading` prop'u ile devre dışı bırak ve yüklenme metni göster.

4.  **Çoklu Dil (i18n) Entegrasyonu:**
    * `PinLoginPage.tsx` içinde `useTranslation('login')` hook'unu kullanarak metinleri yönet.
    * Figma tasarımındaki tüm görünür metinleri (`PIN'inizi Girin`, `Sil`, slogan vb.) `public/locales/en/login.json` ve `public/locales/tr/login.json` dosyalarına yeni anahtarlar olarak ekle ve çevirilerini sağla.

5.  **Rota Güncellemesi:**
    * `atropos-frontend-desktop/src/App.tsx` dosyasını güncelle. Mevcut `/login` rotasını kaldır (veya sakla ama varsayılanı `/login/pin` yap) ve PIN giriş sayfası için yeni rotayı (`path="/login/pin"`) `PinLoginPage` bileşenine yönlendir.
    * Uygulama başladığında kimlik doğrulama yoksa varsayılan olarak `/login/pin` adresine yönlendirmeyi sağla.

6.  **Eski Dosyaları Temizleme:**
    * Önceki "kullanıcı adı/şifre" giriş sayfası olan `LoginPage.tsx` ve buna ait `LoginPage.module.css` (eğer varsa) dosyalarını tamamen kaldır.

---

**AI Geliştirici:** Bu talimat setini eksiksiz bir şekilde uygulayarak, belirtilen Figma tasarımlarını temel alan, hem backend hem de frontend entegrasyonu tamamlanmış, çok dilli ve responsive bir PIN ile giriş sistemi oluştur.

**İşlem tamamlandığında çıktıyı ve uygulamanın durumunu bana bildir.**