'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const createContext = require('../../utils/create-context.cjs');

const [ComboboxItemPropsProvider, useComboboxItemPropsContext] = createContext.createContext({
  name: "ComboboxItemPropsContext",
  hookName: "useComboboxItemPropsContext",
  providerName: "<ComboboxItemPropsProvider />"
});

exports.ComboboxItemPropsProvider = ComboboxItemPropsProvider;
exports.useComboboxItemPropsContext = useComboboxItemPropsContext;
