{"name": "framer-motion", "version": "12.23.9", "description": "A simple and powerful JavaScript animation library", "main": "dist/cjs/index.js", "module": "dist/es/index.mjs", "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/es/index.mjs", "default": "./dist/cjs/index.js"}, "./debug": {"types": "./dist/debug.d.ts", "require": "./dist/cjs/debug.js", "import": "./dist/es/debug.mjs", "default": "./dist/cjs/debug.js"}, "./dom/mini": {"types": "./dist/dom-mini.d.ts", "require": "./dist/cjs/dom-mini.js", "import": "./dist/es/dom-mini.mjs", "default": "./dist/cjs/dom-mini.js"}, "./dom": {"types": "./dist/dom.d.ts", "require": "./dist/cjs/dom.js", "import": "./dist/es/dom.mjs", "default": "./dist/cjs/dom.js"}, "./client": {"types": "./dist/types/client.d.ts", "require": "./dist/cjs/client.js", "import": "./dist/es/client.mjs", "default": "./dist/cjs/client.js"}, "./m": {"types": "./dist/m.d.ts", "require": "./dist/cjs/m.js", "import": "./dist/es/m.mjs", "default": "./dist/cjs/m.js"}, "./mini": {"types": "./dist/mini.d.ts", "require": "./dist/cjs/mini.js", "import": "./dist/es/mini.mjs", "default": "./dist/cjs/mini.js"}, "./projection": {"import": "./dist/es/projection.mjs", "default": "./dist/es/projection.mjs"}, "./package.json": "./package.json"}, "types": "dist/types/index.d.ts", "author": "<PERSON>", "license": "MIT", "repository": "https://github.com/motiondivision/motion/", "sideEffects": false, "keywords": ["react animation", "react", "pose", "react pose", "animation", "gestures", "drag", "spring", "popmotion", "framer", "waapi"], "scripts": {"eslint": "yarn run lint", "lint": "yarn eslint src/**/*.ts", "build": "yarn clean && tsc --noEmitOnError -p . && rollup -c && node ./scripts/check-bundle.js", "dev": "yarn watch", "clean": "rm -rf types dist lib", "test": "yarn test-server && yarn test-client", "test-client": "jest --config jest.config.json --max-workers=2", "test-server": "jest --config jest.config.ssr.json", "prettier": "prettier ./src/* --write", "watch": "concurrently -c blue,red -n tsc --noEmitOnError ,rollup --kill-others \"tsc  --noEmitOnError --watch -p . --preserveWatchOutput\" \"rollup --config --watch --no-watch.clearScreen\"", "prepack": "yarn build && yarn measure", "postpublish": "git push --tags", "measure": "rollup -c ./rollup.size.config.mjs"}, "dependencies": {"motion-dom": "^12.23.9", "motion-utils": "^12.23.6", "tslib": "^2.4.0"}, "devDependencies": {"@thednp/dommatrix": "^2.0.11", "@types/three": "0.137.0", "three": "0.137.0"}, "peerDependencies": {"@emotion/is-prop-valid": "*", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/is-prop-valid": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}}, "bundlesize": [{"path": "./dist/size-rollup-motion.js", "maxSize": "34.9 kB"}, {"path": "./dist/size-rollup-m.js", "maxSize": "6 kB"}, {"path": "./dist/size-rollup-dom-animation.js", "maxSize": "17.85 kB"}, {"path": "./dist/size-rollup-dom-max.js", "maxSize": "29.8 kB"}, {"path": "./dist/size-rollup-animate.js", "maxSize": "19.1 kB"}, {"path": "./dist/size-rollup-scroll.js", "maxSize": "5.2 kB"}, {"path": "./dist/size-rollup-waapi-animate.js", "maxSize": "2.26 kB"}], "gitHead": "a4d48ded6053cd78e573bc54b73f00f8e7c62f7f"}